import os
import copy
import time
import datetime
from typing import Optional, Union, Dict, Any
import base64
import hashlib
from json import dumps as json_dumps
import httpx
from Crypto.Cipher import AES
import json
import anyio
import pandas as pd
from collections import defaultdict
import asyncio
import glob
import plotly.graph_objects as go
from plotly.subplots import make_subplots



# 封装lxopenapi
class LingxingAPI:
    BLOCK_SIZE = 16   # Bytes
    def __init__(self, host: str, app_id: str, app_secret: str, **req_kws):
        self.host = host
        self.app_id = app_id
        self.app_secret = app_secret
        self.req_kws = req_kws
        self.token = None
    
    @staticmethod
    async def get_public_ip():
        async with httpx.AsyncClient() as client:
            response = await client.get("https://toolbox.lingxing.com/api/getIp")
            response.raise_for_status()
            data = response.json()

        ip = data.get("data", "unknown")
        if ':' in ip:
            ip = ip.split(':')[-1].strip()
        return ip
    
    @staticmethod
    def format_params(request_params: Union[None, dict] = None) -> str:
        if not request_params or not isinstance(request_params, dict):
            raise ValueError(f"Invalid Input {request_params}")

        canonical_strs = []
        sort_keys = sorted(request_params.keys())
        for k in sort_keys:
            v = request_params[k]
            if v == "":
                continue
            elif isinstance(v, (dict, list)):
                canonical_strs.append(f"{k}={json_dumps(v, sort_keys=True)}")
            else:
                canonical_strs.append(f"{k}={v}")
        return "&".join(canonical_strs)
    
    @staticmethod
    def md5_encrypt(text: str):
        md = hashlib.md5()
        md.update(text.encode('utf-8'))
        return md.hexdigest()
    
    @classmethod
    def do_pad(cls, text):
        return text + (cls.BLOCK_SIZE - len(text) % cls.BLOCK_SIZE) * \
            chr(cls.BLOCK_SIZE - len(text) % cls.BLOCK_SIZE)
    
    @classmethod
    def aes_encrypt(cls, key, data):
        key = key.encode('utf-8')
        data = cls.do_pad(data)
        cipher = AES.new(key, AES.MODE_ECB)
        result = cipher.encrypt(data.encode())
        encode_str = base64.b64encode(result)
        enc_text = encode_str.decode('utf-8')
        return enc_text
    
    @classmethod
    def generate_sign(cls, encrypt_key: str, params: dict) -> str:
        params_s = cls.format_params(params)
        md5_str = cls.md5_encrypt(params_s).upper()
        sign = cls.aes_encrypt(encrypt_key, md5_str)
        return sign
    
    @staticmethod
    async def request(method: str, req_url: str,
                     params: Optional[dict] = None,
                     json: Optional[dict] = None,
                     headers: Optional[dict] = None,
                     default_timeout=30, proxy=None, **kwargs) -> dict:
        timeout = kwargs.pop('timeout', default_timeout)
        json_str = json_dumps(json, sort_keys=True) if json else None
        async with httpx.AsyncClient(proxy=proxy) as client:
            response = await client.request(
                method=method,
                url=req_url,
                params=params,
                content=json_str,
                timeout=timeout,
                headers=headers,
                **kwargs
            )
            response.raise_for_status()
            return response.json()
    
    async def access_token(self) -> dict:
        path = '/api/auth-server/oauth/access-token' 
        req_url = self.host + path
        req_params = {
            "appId": self.app_id,
            "appSecret": self.app_secret,
        }
        resp = await self.request("POST", req_url, params=req_params, **self.req_kws)
        if resp['code'] != '200':
            error_msg = f"generate_access_token failed, reason: {resp['msg']}"
            raise ValueError(error_msg)

        assert isinstance(resp['data'], dict)
        self.token = resp['data']
        return self.token
    
    async def refresh_token(self, refresh_token: str) -> dict:
        path = '/api/auth-server/oauth/refresh'
        req_url = self.host + path
        req_params = {
            "appId": self.app_id,
            "refreshToken": refresh_token,
        }
        resp = await self.request("POST", req_url, params=req_params, **self.req_kws)
        if resp['code'] != '200':
            error_msg = f"refresh_token failed, reason: {resp['msg']}"
            raise ValueError(error_msg)

        assert isinstance(resp['data'], dict)
        self.token = resp['data']
        return self.token
    
    async def api_request(self, route_name: str, method: str,
                      req_params: Optional[dict] = None,
                      req_body: Optional[dict] = None,
                      access_token: Optional[str] = None,
                      **kwargs) -> dict:
        
        if access_token is None:
            if self.token is None:
                self.token = await self.access_token()
            access_token = self.token['access_token']

        req_url = self.host + route_name
        headers = kwargs.pop('headers', {})

        req_params = req_params or {}

        gen_sign_params = copy.deepcopy(req_params)
        if req_body:
            gen_sign_params.update(req_body)

        timestamp=int(time.time())
        sign_params = {
            "app_key": self.app_id,
            "access_token": access_token,
            "timestamp": timestamp,
        }
        gen_sign_params.update(sign_params)
        sign_params['sign'] = self.generate_sign(self.app_id, gen_sign_params)

        req_params.update(sign_params)

        if req_body and 'Content-Type' not in headers:
            headers['Content-Type'] = 'application/json'

        return await self.request(
            method, req_url, params=req_params,
            headers=headers,
            json=req_body,
            **self.req_kws,
            **kwargs)

# 获取店铺列表
async def GetSellerLists():
    api = LingxingAPI(
        host="https://openapi.lingxing.com",
        app_id="ak_MJOgYw9POgBN8",
        app_secret="/FlD6FYxpwT7IKSu/yKFIg==",
        proxy="http://alfa:<EMAIL>:3128",
    )

    resp = await api.api_request(
        "/erp/sc/data/seller/lists", "GET",
    )

    # 规划文件夹格式
    if not os.path.exists('店铺列表'):
        os.makedirs('店铺列表')

    save_dir = '店铺列表'
    save_path1 = os.path.join(save_dir, '店铺列表.json')
    save_path2 = os.path.join(save_dir, '店铺列表.xlsx')

    # 保存结果
    with open(save_path1, "w", encoding="utf-8") as f:
        json.dump(resp, f, ensure_ascii=False, indent=2)
    print("店铺列表.json 保存完成")

    # json数据转excel
    df = pd.DataFrame(resp.get('data', []))

    # 店铺名放第一列
    if 'name' in df.columns:
        cols = ['name'] + [col for col in df.columns if col != 'name']
        df = df[cols]
    
    # 创建excel写入器
    with pd.ExcelWriter(save_path2, engine='xlsxwriter') as writer:
        # 写入数据
        df.to_excel(writer, sheet_name='店铺列表', index=False)
        # 获取工作簿和工作表
        workbook = writer.book
        worksheet = writer.sheets['店铺列表']
        
        # 定义表头格式
        header_format = workbook.add_format({
            'bold': True,
            'fg_color': '#D7E4BC',
            'border': 1
        })
        
        # 应用表头格式
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # 自适应列宽
        for col_num, column in enumerate(df.columns):
            max_len = max(
                df[column].astype(str).map(len).max(),
                len(str(column))
            ) + 2
            worksheet.set_column(col_num, col_num, min(max_len, 50))
        
        # 冻结首行并添加筛选器
        worksheet.freeze_panes(1, 0)
        worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)
    
    print("店铺列表.xlsx 保存完成")

def CheckSellerLists(name):
    # 读取excel
    df = pd.read_excel('店铺列表/店铺列表.xlsx')
    # 查找店铺名
    if name in df['name'].values:
        row_num = df[df['name'] == name].index[0] + 2
        print(f"找到 {name} 店铺")
        print(f"sid: {df.loc[df['name'] == name, 'sid'].values[0]}")
        print(f"数据来源: 店铺列表.xlsx 第 {row_num} 行\n")
        return True
    else:
        print(f"未找到 {name} 店铺\n")
        return False

def CheckSid(sid):
    # 读取excel
    df = pd.read_excel('店铺列表/店铺列表.xlsx')
    # 查找sid
    sid = int(sid)
    matched_rows = df[df['sid'] == sid]
    if not matched_rows.empty:
        seller_name = matched_rows['name'].values[0]
        row_num = matched_rows.index[0] + 2

        return {
            'found': True,
            'sid': sid,
            'name': seller_name,
            'row_num': row_num
        }
    else:
        print(f"未找到 sid: {sid}\n")
        return {
            'found': False,
            'sid': sid,
            'name': None,
            'row_num': None
        }

async def GetProductPerformance(length, sid, date):
    api = LingxingAPI(
        host="https://openapi.lingxing.com",
        app_id="ak_MJOgYw9POgBN8",
        app_secret="/FlD6FYxpwT7IKSu/yKFIg==",
        proxy="http://alfa:<EMAIL>:3128",
    )

    # 添加请求间隔
    await asyncio.sleep(1)  # 1秒间隔

    resp = await api.api_request(
        "/bd/productPerformance/openApi/asinList",
        "POST",
        req_body={
            "offset": 0,
            "length": length,
            "sort_field": 'volume',
            "sort_type": 'desc',
            "sid": sid,
            "start_date": date,
            "end_date": date,
            "summary_field": 'asin',
        },
    )
    
    seller_info = CheckSid(sid)
    seller_name = seller_info['name']

    # 保存
    if not os.path.exists(f'{seller_name}'):
        os.makedirs(f'{seller_name}')
    # 创建产品表现子文件夹
    save_dir = os.path.join(f'{seller_name}', '产品表现', 'json')
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    save_path1 = os.path.join(save_dir, "length.json")
    save_path2 = os.path.join(save_dir, f"{date}_{seller_name}_产品表现.json")

    # 保存JSON文件
    if length == 0:
        with open(save_path1, "w", encoding="utf-8") as f:
            json.dump(resp, f, ensure_ascii=False, indent=2)
            print(f"length.json 保存完成")
    else:
        with open(save_path2, "w", encoding="utf-8") as f:
            json.dump(resp, f, ensure_ascii=False, indent=2)
            print(f"{date}_{seller_name}_产品表现.json 保存完成")

def GetTotal(seller_name):
    json_dir = os.path.join(seller_name, '产品表现', 'json')
    length_path = os.path.join(json_dir, 'length.json')
    
    # 读取JSON文件
    with open(length_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 检查返回状态
    if data.get('code') != 0:
        print(f"获取总数据失败: {data.get('msg', '未知错误')}")
        return None
    
    return data.get('data', {}).get('total', 0)

def CheckDate(seller_name,start_date, end_date):
    # 转换日期字符串为日期对象
    start = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
    end = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
    # 生成日期范围内的所有日期
    date_range = []
    current_date = start
    while current_date <= end:
        date_range.append(current_date)
        current_date += datetime.timedelta(days=1)
    
    # 获取实际存在的文件日期
    json_dir = os.path.join(seller_name, '产品表现', 'json')
    existing_dates = set()
    
    if os.path.exists(json_dir):
        for filename in os.listdir(json_dir):
            date_str = ExtractDateFromFilename(filename)
            if date_str:
                file_date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
                existing_dates.add(file_date)

    # 找出缺失的日期
    missing_dates = []
    for date in date_range:
        if date not in existing_dates:
            missing_dates.append(date.strftime("%Y-%m-%d"))

    print(f'缺少日期: {len(missing_dates)} 天')
    
    return missing_dates

# 提取日期
def ExtractDateFromFilename(filename):
    date_str = filename.split('_')[0]
    try:
        datetime.datetime.strptime(date_str, '%Y-%m-%d')
        return date_str
    except ValueError:
        return None

# 安全转换为整数
def SafeInt(value, default=0):    
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

# 安全转换为浮点数
def SafeFloat(value, default=0.0):
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default

# 基础产品信息
def ExtractKeyData(product, date):
    data = {
            '日期': date,
            'ASIN': '',    # 将在后续填充
            '父ASIN': '',    # 将在后续填充
            '产品标题': product.get('item_name', ''),
            '品牌': ', '.join(product.get('brands', [])),
            '分类': ', '.join(product.get('categories', [])),
            '负责人': ', '.join(product.get('principal_names', [])),
            '开发人': ', '.join(product.get('developer_names', [])),
            '币种': product.get('currency_icon', '$'),

            # 销售数据
            '销量': SafeInt(product.get('volume')),
            '销售额': SafeFloat(product.get('amount')),
            '订单量': SafeInt(product.get('order_items')),
            '销量环比': SafeFloat(product.get('volume_chain_ratio')),
            '销售额环比': SafeFloat(product.get('amount_chain_ratio')),
            '订单量环比': SafeFloat(product.get('order_chain_ratio')),
            '环比销量': SafeInt(product.get('volume_chain')),
            '环比销售额': SafeFloat(product.get('amount_chain')),
            '环比订单量': SafeInt(product.get('order_items_chain')),

            # B2B数据
            'B2B销量': SafeInt(product.get('b2b_volume')),
            'B2B销售额': SafeFloat(product.get('b2b_amount')),
            'B2B订单量': SafeInt(product.get('b2b_order_items')),

            # 促销数据
            '促销销量': SafeInt(product.get('promotion_volume')),
            '促销销售额': SafeFloat(product.get('promotion_amount')),
            '促销订单量': SafeInt(product.get('promotion_order_items')),
            '促销折扣': SafeFloat(product.get('promotion_discount')),

            # 利润数据
            '结算毛利润': SafeFloat(product.get('gross_profit')),
            '订单毛利润': SafeFloat(product.get('predict_gross_profit')),
            '结算毛利率': SafeFloat(product.get('gross_margin')),
            '订单毛利率': SafeFloat(product.get('predict_gross_margin')),
            'ROI': SafeFloat(product.get('roi')),
            '净销售额': SafeFloat(product.get('net_amount')),

            # 库存信息
            'FBA可售': SafeInt(product.get('afn_fulfillable_quantity')),
            'FBA入库中': SafeInt(product.get('afn_inbound_receiving_quantity')),
            'FBA在途': SafeInt(product.get('afn_inbound_shipped_quantity')),
            'FBA计划入库': SafeInt(product.get('afn_inbound_working_quantity')),
            'FBA不可售': SafeInt(product.get('afn_unsellable_quantity')),
            '调仓中': SafeInt(product.get('reserved_fc_processing')),
            '待调仓': SafeInt(product.get('reserved_fc_transfers')),
            '待发货': SafeInt(product.get('reserved_customerorders')),
            '真实库存': SafeInt(product.get('afn_fulfillable_quantity')) + SafeInt(product.get('reserved_fc_processing')) + SafeInt(product.get('reserved_fc_transfers')) + SafeInt(product.get('reserved_customerorders')),
            'FBM可售': SafeInt(product.get('fbm_quantity')),
            '实际在途': SafeInt(product.get('stock_up_num')),
            '可售预估天数': SafeInt(product.get('available_days')),
            'FBM可售天数': SafeInt(product.get('fbm_available_days')),
            '月库销比': SafeFloat(product.get('month_stock_sales_ratio')),
    
            # 广告数据
            '广告花费': SafeFloat(product.get('spend')),
            'SP广告费': SafeFloat(product.get('ads_sp_cost')),
            'SD广告费': SafeFloat(product.get('ads_sd_cost')),
            'SB广告费': SafeFloat(product.get('shared_ads_sb_cost')),
            'SBV广告费': SafeFloat(product.get('shared_ads_sbv_cost')),
            '广告销售额': SafeFloat(product.get('ad_sales_amount')),
            'SP广告销售额': SafeFloat(product.get('ads_sp_sales')),
            'SD广告销售额': SafeFloat(product.get('ads_sd_sales')),
            'SB广告销售额': SafeFloat(product.get('shared_ads_sb_sales')),
            'SBV广告销售额': SafeFloat(product.get('shared_ads_sbv_sales')),
            '广告订单量': SafeInt(product.get('ad_order_quantity')),
            '直接成交订单量': SafeInt(product.get('ad_direct_order_quantity')),
            '直接成交销售额': SafeFloat(product.get('ad_direct_sales_amount')),
            '点击量': SafeInt(product.get('clicks')),
            '展示量': SafeInt(product.get('impressions')),
            'CTR': SafeFloat(product.get('ctr')),
            'CPC': SafeFloat(product.get('cpc')),
            'CPO': SafeFloat(product.get('cpo')),
            'CPM': SafeFloat(product.get('cpm')),
            'ACOS': SafeFloat(product.get('acos')),
            'ACOAS': SafeFloat(product.get('acoas')),
            'ROAS': SafeFloat(product.get('roas')),
            'ASOAS': SafeFloat(product.get('asoas')),
            '广告CVR': SafeFloat(product.get('ad_cvr')),
            '广告订单量占比': SafeFloat(product.get('adv_rate')),
    
            # 流量数据
            'Sessions总计': SafeInt(product.get('sessions_total')),
            'Sessions浏览器': SafeInt(product.get('sessions')),
            'Sessions移动端': SafeInt(product.get('sessions_mobile')),
            'PV总计': SafeInt(product.get('page_views_total')),
            'PV浏览器': SafeInt(product.get('page_views')),
            'PV移动端': SafeInt(product.get('page_views_mobile')),
            'CVR': SafeFloat(product.get('cvr')),
            '销量CVR': SafeFloat(product.get('volume_cvr')),
            'Buybox': SafeFloat(product.get('buy_box_percentage')),

            # 评价信息
            '评论数': SafeInt(product.get('reviews_count')),
            '评分': SafeFloat(product.get('avg_star')),
            '前一个评分': SafeFloat(product.get('prev_star')),
            '留评率': SafeFloat(product.get('comment_rate')),

            # 退货信息
            '退款量': SafeInt(product.get('return_count')),
            '退款率': SafeFloat(product.get('return_rate')),
            '退货量': SafeInt(product.get('return_goods_count')),
            '退货率': SafeFloat(product.get('return_goods_rate')),
            '退款金额': SafeFloat(product.get('return_amount')),

            # 排名信息
            '大类排名': SafeInt(product.get('cate_rank')),
            '上次大类排名': SafeInt(product.get('prev_cate_rank')),
            '排名分类': product.get('rank_category', ''),

            # 价格信息
            '平均售价': SafeFloat(product.get('avg_landed_price')),
            '销售均价': SafeFloat(product.get('avg_custom_price')),
            '平均销量': SafeFloat(product.get('avg_volume')),

            # 其他信息
            '店铺ID': ', '.join(map(str, product.get('sids', []))),
            '小图链接': product.get('small_image_url', ''),
        }

    # 处理小类排名
    small_cate_ranks = product.get('small_cate_rank', [])
    if small_cate_ranks:
        for i, rank_info in enumerate(small_cate_ranks[:3]):  # 最多显示3个小类排名
            data[f'小类排名{i+1}_类别'] = rank_info.get('category', '')
            data[f'小类排名{i+1}_排名'] = SafeInt(rank_info.get('rank'))
            data[f'小类排名{i+1}_上次排名'] = SafeInt(rank_info.get('prev_rank'))

    return data

def ProcessJsonFiles(seller_name):
    # 构建输入输出路径
    input_dir = os.path.join(seller_name, "产品表现", "json")
    output_dir = os.path.join(seller_name, "产品表现", "excel")
    
    print(f"开始处理店铺 {seller_name} 的JSON文件")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 按ASIN分组存储数据
    asin_data = defaultdict(list)

    # 遍历所有JSON文件
    json_files = [f for f in os.listdir(input_dir) if f.endswith('.json') and "_产品表现.json" in f]
    print(f"找到 {len(json_files)} 个JSON文件")

    for filename in json_files:
        file_path = os.path.join(input_dir, filename)
        date = ExtractDateFromFilename(filename)

        if not date:
            print(f"跳过无法解析日期的文件: {filename}")
            continue

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # 检查数据结构
                if 'data' not in data or 'list' not in data['data']:
                    print(f"跳过数据结构异常的文件: {filename}")
                    continue

                # 处理每个产品
                for product in data['data']['list']:
                    # 提取关键数据
                    product_data = ExtractKeyData(product, date)

                    # 获取ASIN列表
                    asins = product.get('asins', [])
                    parent_asins = product.get('parent_asins', [])

                    # 为每个ASIN创建记录
                    for asin_info in asins:
                        asin = asin_info.get('asin', '')
                        if asin:
                            record = product_data.copy()
                            record['ASIN'] = asin
                            record['亚马逊链接'] = asin_info.get('amazon_url', '')

                            # 添加父ASIN信息
                            if parent_asins:
                                record['父ASIN'] = parent_asins[0].get('parent_asin', '')
                                record['父ASIN链接'] = parent_asins[0].get('amazon_url', '')

                            asin_data[asin].append(record)

        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")

    print(f"共处理 {len(asin_data)} 组ASIN的数据")

    # 为每个ASIN创建优化的Excel文件
    for asin, records in asin_data.items():
        CreateOptimizedExcel(asin, records, output_dir)

    print(f"店铺 {seller_name} 数据处理完成")

def CreateOptimizedExcel(asin, records, output_folder):
    # 创建DataFrame并按日期排序
    df = pd.DataFrame(records)
    df['日期'] = pd.to_datetime(df['日期']).dt.date
    df = df.sort_values('日期')

    # 处理文件名中的特殊字符
    safe_asin = asin.replace(':', '_').replace('/', '_').replace('\\', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
    output_path = os.path.join(output_folder, f"{safe_asin}.xlsx")

    # 创建Excel写入器
    writer = pd.ExcelWriter(output_path, engine='xlsxwriter')
    workbook = writer.book

    # 定义格式
    header_format = workbook.add_format({
        'bold': True,
        'text_wrap': True,
        'valign': 'top',
        'fg_color': '#D7E4BC',
        'border': 1
    })

    date_format = workbook.add_format({
        'num_format': 'yyyy-mm-dd',
        'border': 1
    })

    number_format = workbook.add_format({
        'num_format': '#,##0',
        'border': 1
    })

    currency_format = workbook.add_format({
        'num_format': '$#,##0.00',
        'border': 1
    })

    percentage_format = workbook.add_format({
        'num_format': '0.00%',
        'border': 1
    })

    decimal_format = workbook.add_format({
        'num_format': '0.0000',
        'border': 1
    })

    # 写入数据到工作表
    df.to_excel(writer, sheet_name='产品表现数据', index=False)
    worksheet = writer.sheets['产品表现数据']

    # 应用格式
    for col_num, column in enumerate(df.columns):
        # 设置表头格式
        worksheet.write(0, col_num, column, header_format)

        # 根据列名设置不同的格式
        if '日期' in column:
            worksheet.set_column(col_num, col_num, 12, date_format)
        elif any(keyword in column for keyword in ['销售额', '金额', '利润', '花费', '折扣']):
            worksheet.set_column(col_num, col_num, 15, currency_format)
        elif any(keyword in column for keyword in ['率', 'ROI', 'CTR', 'CVR', 'ACOS', 'ROAS']):
            worksheet.set_column(col_num, col_num, 12, percentage_format)
        elif any(keyword in column for keyword in ['CPC', 'CPO', 'CPM', '评分', '均价']):
            worksheet.set_column(col_num, col_num, 12, decimal_format)
        elif any(keyword in column for keyword in ['量', '数', '排名', '天数']):
            worksheet.set_column(col_num, col_num, 12, number_format)
        elif column in ['ASIN', '父ASIN']:
            worksheet.set_column(col_num, col_num, 12)
        elif column == '产品标题':
            worksheet.set_column(col_num, col_num, 50)
        elif '链接' in column:
            worksheet.set_column(col_num, col_num, 30)
        else:
            # 自动调整列宽
            max_len = max(
                df[column].astype(str).map(len).max(),
                len(str(column))
            ) + 2
            worksheet.set_column(col_num, col_num, min(max_len, 30))

    # 冻结首行
    worksheet.freeze_panes(1, 0)

    # 添加筛选器
    worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)

    writer.close()
    print(f"创建文件: {output_path}  共 {len(df)} 条记录")

# 断货曲线分析
def LoadAndPrepareData(file_path):
    # 加载并准备数据
    df = pd.read_excel(file_path)

    # 转换日期列
    df['日期'] = pd.to_datetime(df['日期'])

    # 按日期排序
    df = df.sort_values('日期').reset_index(drop=True)

    # 计算库存变化
    df['库存变化'] = df['真实库存'].diff()

    # 计算不同周期的平均销量
    df['7日平均销量'] = df['销量'].rolling(window=7, min_periods=1).mean()
    df['14日平均销量'] = df['销量'].rolling(window=14, min_periods=1).mean()
    df['30日平均销量'] = df['销量'].rolling(window=30, min_periods=1).mean()

    # 计算不同周期的预测断货天数
    df['预测断货天数_7日'] = df['真实库存'] / df['7日平均销量'].replace(0, 1)
    df['预测断货天数_14日'] = df['真实库存'] / df['14日平均销量'].replace(0, 1)
    df['预测断货天数_30日'] = df['真实库存'] / df['30日平均销量'].replace(0, 1)
    
    return df

# 创建交互式断货曲线图
def CreateInteractiveStockoutCurve(df, asin):
    # 获取最小和最大日期范围
    min_date = df['日期'].min()
    max_date = df['日期'].max()
    
    # 创建子图
    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=(
            f'{asin} - 真实库存趋势',
            f'{asin} - 销量与库存变化',
            f'{asin} - 可售天数预警'
        ),
        specs=[[{"secondary_y": False}],
               [{"secondary_y": True}],
               [{"secondary_y": False}]],
        vertical_spacing=0.08,
        shared_xaxes=True
    )
    
    # 图1: 真实库存趋势
    fig.add_trace(
        go.Scatter(
            x=df['日期'],
            y=df['真实库存'],
            mode='lines',
            name='真实库存',
            line=dict(color='blue', width=2),
            fill='tozeroy',
            fillcolor='rgba(0,0,255,0.2)',
            hovertemplate='真实库存: %{y}<extra></extra>'
        ),
        row=1, col=1
    )

    # 低库存警戒线
    low_stock_threshold = df['真实库存'].quantile(0.2)
    fig.add_hline(
        y=low_stock_threshold,
        line_dash="dash",
        line_color="red",
        annotation_text=f"低库存警戒线 ({low_stock_threshold:.0f})",
        row=1, col=1
    )

    # 断货点
    stockout_points = df[df['真实库存'] == 0]
    if not stockout_points.empty:
        fig.add_trace(
            go.Scatter(
                x=stockout_points['日期'],
                y=stockout_points['真实库存'],
                mode='markers',
                name='断货点',
                marker=dict(color='red', size=8),
                hovertemplate='断货<extra></extra>'
            ),
            row=1, col=1
        )
    
    # 图2: 销量与库存变化
    fig.add_trace(
        go.Bar(
            x=df['日期'],
            y=df['销量'],
            name='销量',
            marker_color='green',
            opacity=0.6,
            hovertemplate='销量: %{y}<extra></extra>'
        ),
        row=2, col=1
    )

    fig.add_trace(
        go.Scatter(
            x=df['日期'],
            y=df['库存变化'],
            mode='lines',
            name='库存变化',
            line=dict(color='orange', width=2),
            hovertemplate='库存变化: %{y}<extra></extra>'
        ),
        row=2, col=1,
        secondary_y=True
    )
    
    # 图3: 可售天数预警
    fig.add_trace(
        go.Scatter(
            x=df['日期'],
            y=df['预测断货天数_7日'],
            mode='lines',
            name='7日预测断货天数',
            line=dict(color='purple', width=2),
            hovertemplate='7日预测: %{y:.1f}天<extra></extra>'
        ),
        row=3, col=1
    )

    fig.add_trace(
        go.Scatter(
            x=df['日期'],
            y=df['预测断货天数_14日'],
            mode='lines',
            name='14日预测断货天数',
            line=dict(color='blue', width=2),
            hovertemplate='14日预测: %{y:.1f}天<extra></extra>'
        ),
        row=3, col=1
    )

    fig.add_trace(
        go.Scatter(
            x=df['日期'],
            y=df['预测断货天数_30日'],
            mode='lines',
            name='30日预测断货天数',
            line=dict(color='green', width=2),
            hovertemplate='30日预测: %{y:.1f}天<extra></extra>'
        ),
        row=3, col=1
    )
    
    # 添加断货预警线
    fig.add_hline(
        y=7,
        line_dash="dash",
        line_color="red",
        annotation_text="断货预警线 (7天)",
        row=3, col=1
    )
    
    # 更新布局
    fig.update_layout(
        height=900,
        showlegend=True,
        hovermode='x unified',
        title_text=f"{asin} - 库存与销售分析",
        margin=dict(t=100, b=100)
    )
    
    fig.update_xaxes(
        range=[min_date, max_date],
        row=3, col=1
    )
    
    return fig

def PlotStockoutCurves(seller_name):
    # 输入和输出路径
    input_dir = os.path.join(seller_name, "产品表现", "excel")
    output_dir = os.path.join(seller_name, "产品表现", "html")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有Excel文件
    excel_files = glob.glob(os.path.join(input_dir, "*.xlsx"))
    
    if not excel_files:
        print(f"在目录 {input_dir} 中没有找到Excel文件")
    else:     
        for file_path in excel_files:
            try:
                # 从文件名提取ASIN
                filename = os.path.basename(file_path)
                asin = os.path.splitext(filename)[0]
                
                # 读取和处理数据
                df = LoadAndPrepareData(file_path)
                
                # 创建交互式图表
                fig = CreateInteractiveStockoutCurve(df, asin)
                
                # 保存为HTML文件
                output_path = os.path.join(output_dir, f"{asin}.html")
                fig.write_html(output_path, include_plotlyjs='cdn')
                
                print(f"交互式图表已保存到: {output_path}")
                
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {str(e)}")
                continue



def main():
    anyio.run(GetSellerLists)
    print(
        "\n欢迎使用自动化脚本\n"
        "专为高效电商运营打造的智能工具\n"
        "可以在店铺列表中查看有哪些店铺\n"
        "脚本功能：\n"
        "使用过程中请不要打开相关文件以免影响脚本运行\n"
        "查询店铺sid请输入1\n"
        "通过sid查询店铺请输入2\n"
        "获取产品表现请输入3\n"

        "退出请按Q\n"
    )
    choice = input("请输入您的选择:")
    while(choice != 'Q'):
        if choice == '1':
            while True:
                name = input("请输入店铺名:")
                if name.upper() == 'Q':
                    break
                if CheckSellerLists(name) == True:
                    break
        
        elif choice == '2':
            while True:
                sid = input("请输入店铺sid:")
                if sid.upper() == 'Q':
                    break
                result = CheckSid(sid)
                print(f"找到 sid: {sid}")
                print(f"店铺名: {result['name']}") 
                print(f"数据来源: 店铺列表.xlsx 第 {result['row_num']} 行\n")
                if result['found']:
                    break

        elif choice == '3':
            while True:
                sid = input("请输入sid:")
                start_date = input("请输入起始日期(YYYY-MM-DD):")
                end_date = input("请输入结束日期(YYYY-MM-DD):")
                if sid.upper() == 'Q' or start_date.upper() == 'Q' or end_date.upper() == 'Q':
                    break

                sid_result = CheckSid(int(sid))
                if not sid_result['found']:
                    print("sid不存在\n")
                    continue

                # 检查日期格式是否正确
                try:
                    start_dt = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
                    end_dt = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
                except ValueError:
                    print("日期格式不正确")
                    continue

                # 检查结束日期是否在未来
                today = datetime.date.today()
                if end_dt > today:
                    print("结束日期不能是未来日期")
                    continue

                # 检查起始日期是否早于结束日期
                if start_dt > end_dt:
                    print("起始日期不能晚于结束日期")
                    continue

                # 确保目录存在
                seller_name = sid_result['name']
                if not os.path.exists(f'{seller_name}'):
                    os.makedirs(f'{seller_name}')
                if not os.path.exists(os.path.join(seller_name, '产品表现', 'json')):
                    os.makedirs(os.path.join(seller_name, '产品表现', 'json'))

                # 先获取length.json
                anyio.run(GetProductPerformance, 0, int(sid), start_date)

                time.sleep(3)
                total = GetTotal(seller_name)
                missing_dates = CheckDate(seller_name, start_date, end_date)

                for i, date in enumerate(missing_dates, 1):
                    anyio.run(GetProductPerformance, total, int(sid), date)
                    print(f"[{i} / {len(missing_dates)}]")
                
                ProcessJsonFiles(seller_name)
                PlotStockoutCurves(seller_name)

                print('完成')
                break
                
                

        else:
            print("输入有误，请重新输入")
            continue

        choice = input("请输入您的选择: ")

    print("\n谢谢使用")



if __name__ == '__main__':
    main()